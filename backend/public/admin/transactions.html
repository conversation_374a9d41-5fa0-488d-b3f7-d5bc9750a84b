<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transactions - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #FF6B00;
            font-size: 24px;
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-links a {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .nav-links a:hover {
            background-color: #f0f0f0;
        }

        .nav-links a.active {
            background-color: #FF6B00;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #FF6B00;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: #FF6B00;
            color: white;
        }

        .btn-primary:hover {
            background-color: #e55a00;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .transactions-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        td {
            font-size: 14px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .amount {
            font-weight: 600;
            color: #28a745;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background-color: #f8f9fa;
        }

        .pagination button.active {
            background-color: #FF6B00;
            color: white;
            border-color: #FF6B00;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            table {
                font-size: 12px;
            }
            
            th, td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Restaurant Admin - Transactions</h1>
            <div class="nav-links">
                <a href="/admin/settings">Settings</a>
                <a href="/admin/transactions.html" class="active">Transactions</a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalTransactions">-</div>
                <div class="stat-label">Total Transactions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completedTransactions">-</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalRevenue">-</div>
                <div class="stat-label">Total Revenue</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="todayRevenue">-</div>
                <div class="stat-label">Today's Revenue</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <div class="filter-group">
                <label>Status</label>
                <select id="statusFilter">
                    <option value="">All Status</option>
                    <option value="completed">Completed</option>
                    <option value="pending">Pending</option>
                    <option value="failed">Failed</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Search</label>
                <input type="text" id="searchInput" placeholder="Restaurant, Plan, Transaction ID...">
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <button class="btn btn-primary" onclick="loadTransactions()">Filter</button>
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <button class="btn btn-secondary" onclick="clearFilters()">Clear</button>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="transactions-table">
            <div class="table-header">
                <div class="table-title">Recent Transactions</div>
                <button class="btn btn-primary" onclick="loadTransactions()">🔄 Refresh</button>
            </div>
            
            <div id="loadingState" class="loading">
                Loading transactions...
            </div>
            
            <div id="errorState" class="error" style="display: none;">
                Failed to load transactions. Please try again.
            </div>
            
            <div id="tableContainer" style="display: none;">
                <table>
                    <thead>
                        <tr>
                            <th>Sr. No.</th>
                            <th>Restaurant</th>
                            <th>Plan</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment ID</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody id="transactionsTableBody">
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- Pagination will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/transactions/stats');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('totalTransactions').textContent = stats.total_transactions || 0;
                    document.getElementById('completedTransactions').textContent = stats.completed_transactions || 0;
                    document.getElementById('totalRevenue').textContent = `₹${(stats.total_revenue || 0).toLocaleString()}`;
                    document.getElementById('todayRevenue').textContent = `₹${(stats.today_revenue || 0).toLocaleString()}`;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load transactions
        async function loadTransactions(page = 1) {
            const loadingState = document.getElementById('loadingState');
            const errorState = document.getElementById('errorState');
            const tableContainer = document.getElementById('tableContainer');
            
            loadingState.style.display = 'block';
            errorState.style.display = 'none';
            tableContainer.style.display = 'none';
            
            try {
                const status = document.getElementById('statusFilter').value;
                const search = document.getElementById('searchInput').value;
                
                const params = new URLSearchParams({
                    page: page.toString(),
                    per_page: '15'
                });
                
                if (status) params.append('status', status);
                if (search) params.append('search', search);
                
                const response = await fetch(`/api/transactions?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    displayTransactions(data.data);
                    updatePagination(data.pagination);
                    currentPage = page;
                    
                    loadingState.style.display = 'none';
                    tableContainer.style.display = 'block';
                } else {
                    throw new Error(data.message || 'Failed to load transactions');
                }
            } catch (error) {
                console.error('Error loading transactions:', error);
                loadingState.style.display = 'none';
                errorState.style.display = 'block';
                errorState.textContent = error.message || 'Failed to load transactions. Please try again.';
            }
        }

        // Display transactions in table
        function displayTransactions(transactions) {
            const tbody = document.getElementById('transactionsTableBody');
            tbody.innerHTML = '';
            
            if (transactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px; color: #666;">No transactions found</td></tr>';
                return;
            }
            
            transactions.forEach((transaction, index) => {
                const row = document.createElement('tr');
                const serialNumber = ((currentPage - 1) * 15) + index + 1;
                
                row.innerHTML = `
                    <td>${serialNumber}</td>
                    <td>${transaction.restaurant_name || '-'}</td>
                    <td>${transaction.plan_name || '-'}</td>
                    <td class="amount">₹${parseFloat(transaction.amount || 0).toLocaleString()}</td>
                    <td><span class="status-badge status-${transaction.status}">${transaction.status}</span></td>
                    <td><small>${transaction.razorpay_payment_id || transaction.transaction_id || '-'}</small></td>
                    <td>${transaction.payment_date ? new Date(transaction.payment_date).toLocaleDateString() : '-'}</td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // Update pagination
        function updatePagination(pagination) {
            const paginationContainer = document.getElementById('pagination');
            totalPages = pagination.last_page;
            
            let paginationHTML = '';
            
            // Previous button
            paginationHTML += `<button onclick="loadTransactions(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>Previous</button>`;
            
            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                paginationHTML += `<button onclick="loadTransactions(${i})" ${i === currentPage ? 'class="active"' : ''}>${i}</button>`;
            }
            
            // Next button
            paginationHTML += `<button onclick="loadTransactions(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>Next</button>`;
            
            paginationContainer.innerHTML = paginationHTML;
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('searchInput').value = '';
            loadTransactions(1);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadTransactions();
            
            // Add enter key support for search
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loadTransactions(1);
                }
            });
        });
    </script>
</body>
</html>
