@extends('admin.layout')

@section('title', 'Settings')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-start">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Application Settings</h1>
            <p class="mt-2 text-gray-600">Manage application configuration, branding, and subscriptions</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.transactions') }}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                💳 View Transactions
            </a>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <button onclick="showTab('general')" id="general-tab" class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                General Settings
            </button>
            <button onclick="showTab('subscriptions')" id="subscriptions-tab" class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Subscription Plans
            </button>
        </nav>
    </div>

    <!-- General Settings Tab -->
    <div id="general-content" class="tab-content bg-white shadow overflow-hidden sm:rounded-lg card-shadow">
        <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
            @csrf
            
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6">
                    
                    <!-- Application Name -->
                    <div>
                        <label for="application_name" class="block text-sm font-medium text-gray-700">
                            Application Name
                        </label>
                        <div class="mt-1">
                            <input type="text" 
                                   name="application_name" 
                                   id="application_name" 
                                   value="{{ old('application_name', $settings->application_name) }}"
                                   class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                   required>
                        </div>
                        @error('application_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- App Version -->
                    <div>
                        <label for="app_version" class="block text-sm font-medium text-gray-700">
                            App Version
                        </label>
                        <div class="mt-1">
                            <input type="text" 
                                   name="app_version" 
                                   id="app_version" 
                                   value="{{ old('app_version', $settings->app_version) }}"
                                   class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                   required>
                        </div>
                        @error('app_version')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Logo Upload -->
                    <div>
                        <label for="logo" class="block text-sm font-medium text-gray-700">
                            Logo
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                @if($settings->logo)
                                    <img id="logo-preview" 
                                         src="{{ Storage::url($settings->logo) }}" 
                                         alt="Current Logo" 
                                         class="h-20 w-20 object-cover rounded-lg border border-gray-300">
                                @else
                                    <div id="logo-preview" 
                                         class="h-20 w-20 bg-gray-200 rounded-lg border border-gray-300 flex items-center justify-center">
                                        <span class="text-gray-400 text-xs">No Logo</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file" 
                                       name="logo" 
                                       id="logo" 
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                       onchange="previewImage(this, 'logo-preview')">
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('logo')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Favicon Upload -->
                    <div>
                        <label for="favicon" class="block text-sm font-medium text-gray-700">
                            Favicon
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                @if($settings->favicon)
                                    <img id="favicon-preview" 
                                         src="{{ Storage::url($settings->favicon) }}" 
                                         alt="Current Favicon" 
                                         class="h-16 w-16 object-cover rounded border border-gray-300">
                                @else
                                    <div id="favicon-preview" 
                                         class="h-16 w-16 bg-gray-200 rounded border border-gray-300 flex items-center justify-center">
                                        <span class="text-gray-400 text-xs">No Favicon</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file" 
                                       name="favicon" 
                                       id="favicon" 
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                       onchange="previewImage(this, 'favicon-preview')">
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF, ICO up to 1MB</p>
                            </div>
                        </div>
                        @error('favicon')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- App Logo Upload -->
                    <div>
                        <label for="app_logo" class="block text-sm font-medium text-gray-700">
                            Application Logo
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                @if($settings->app_logo)
                                    <img id="app-logo-preview" 
                                         src="{{ Storage::url($settings->app_logo) }}" 
                                         alt="Current App Logo" 
                                         class="h-20 w-20 object-cover rounded-lg border border-gray-300">
                                @else
                                    <div id="app-logo-preview" 
                                         class="h-20 w-20 bg-gray-200 rounded-lg border border-gray-300 flex items-center justify-center">
                                        <span class="text-gray-400 text-xs">No App Logo</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file" 
                                       name="app_logo" 
                                       id="app_logo" 
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                       onchange="previewImage(this, 'app-logo-preview')">
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('app_logo')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Settings
                </button>
            </div>
        </form>
    </div>

    <!-- Subscriptions Tab -->
    <div id="subscriptions-content" class="tab-content hidden">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg card-shadow">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Subscription Plans</h3>
                        <p class="mt-1 text-sm text-gray-600">Manage subscription plans for premium features</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="loadPlans()" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" title="Refresh plans">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                        <button onclick="showAddPlanModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add New Plan
                        </button>
                    </div>
                </div>

                <!-- Subscription Plans Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="plans-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sr. No.</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price (INR)</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="plans-tbody">
                            <!-- Plans will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="fixed top-4 right-4 z-50"></div>

<!-- Add/Edit Plan Modal -->
<div id="plan-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modal-title">Add Subscription Plan</h3>
            <form id="plan-form">
                <div class="mb-4">
                    <label for="plan-name" class="block text-sm font-medium text-gray-700">Plan Name</label>
                    <input type="text" id="plan-name" name="name" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div class="mb-4">
                    <label for="plan-duration" class="block text-sm font-medium text-gray-700">Duration (Days)</label>
                    <input type="number" id="plan-duration" name="duration_days" required min="1" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div class="mb-4">
                    <label for="plan-price" class="block text-sm font-medium text-gray-700">Price (₹ INR)</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">₹</span>
                        </div>
                        <input type="number" id="plan-price" name="price" required min="0" step="0.01"
                               class="pl-7 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="0.00">
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Enter amount in Indian Rupees (INR)</p>
                </div>
                <div class="mb-4">
                    <label for="plan-description" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea id="plan-description" name="description" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="plan-enabled" name="is_enabled" checked class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Enabled</span>
                    </label>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closePlanModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700">
                        Save Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');

    // Add active class to selected tab
    document.getElementById(tabName + '-tab').classList.add('active');
}

// Image preview function
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="h-full w-full object-cover rounded">`;
        };
        reader.readAsDataURL(file);
    }
}

// Subscription plan management
let editingPlanId = null;

// Notification system
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');

    // Safety check - if container doesn't exist, create it
    if (!container) {
        console.error('Notification container not found, creating one...');
        const newContainer = document.createElement('div');
        newContainer.id = 'notification-container';
        newContainer.className = 'fixed top-4 right-4 z-50';
        document.body.appendChild(newContainer);
        return showNotification(message, type); // Retry with new container
    }

    const notification = document.createElement('div');

    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    notification.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-2 transform transition-all duration-300 translate-x-full opacity-0`;
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    container.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

function loadPlans() {
    const tbody = document.getElementById('plans-tbody');

    // Show loading state
    tbody.innerHTML = `
        <tr>
            <td colspan="6" class="px-6 py-8 text-center">
                <div class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mr-3"></div>
                    <span class="text-gray-500">Loading subscription plans...</span>
                </div>
            </td>
        </tr>
    `;

    fetch('/admin/subscription-plans', {
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('Please log in to access this feature');
            } else if (response.status === 403) {
                throw new Error('Admin access required');
            } else {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            renderPlans(data.data);
        } else {
            throw new Error(data.message || 'Failed to load plans');
        }
    })
    .catch(error => {
        console.error('Error loading plans:', error);
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-8 text-center">
                    <div class="text-red-500">
                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="font-medium">Failed to load subscription plans</p>
                        <p class="text-sm text-gray-500 mt-1">${error.message}</p>
                        <button onclick="loadPlans()" class="mt-3 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm">
                            Try Again
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
}

function renderPlans(plans) {
    const tbody = document.getElementById('plans-tbody');
    tbody.innerHTML = '';

    if (plans.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-8 text-center">
                    <div class="text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-lg font-medium">No subscription plans found</p>
                        <p class="text-sm mt-1">Create your first subscription plan to get started.</p>
                        <button onclick="showAddPlanModal()" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                            Add First Plan
                        </button>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    plans.forEach((plan, index) => {
        const row = document.createElement('tr');

        // Format duration for better display
        let durationDisplay = plan.formatted_duration;
        if (plan.duration_days === 30) {
            durationDisplay = '1 Month (30 days)';
        } else if (plan.duration_days === 365) {
            durationDisplay = '1 Year (365 days)';
        } else if (plan.duration_days === 7) {
            durationDisplay = '1 Week (7 days)';
        } else {
            durationDisplay = `${plan.duration_days} days`;
        }

        // Format price in INR with proper formatting
        const priceFormatted = new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(plan.price);

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${index + 1}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${plan.name}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${durationDisplay}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">${priceFormatted}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <button onclick="togglePlanStatus(${plan.id}, ${plan.is_enabled})"
                        id="status-btn-${plan.id}"
                        class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full cursor-pointer transition-all duration-200 border ${plan.is_enabled ? 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200' : 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200'}"
                        title="Click to ${plan.is_enabled ? 'disable' : 'enable'} this plan">
                    <span class="flex items-center">
                        <span class="w-2 h-2 rounded-full mr-2 ${plan.is_enabled ? 'bg-green-500' : 'bg-red-500'}"></span>
                        ${plan.is_enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </button>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editPlan(${plan.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                <button onclick="deletePlan(${plan.id})" class="text-red-600 hover:text-red-900">Delete</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function showAddPlanModal() {
    editingPlanId = null;
    document.getElementById('modal-title').textContent = 'Add Subscription Plan';
    document.getElementById('plan-form').reset();
    document.getElementById('plan-enabled').checked = true;
    document.getElementById('plan-modal').classList.remove('hidden');
}

function togglePlanStatus(planId, currentStatus) {
    const newStatus = !currentStatus;
    const statusText = newStatus ? 'enable' : 'disable';
    const button = document.getElementById(`status-btn-${planId}`);

    if (confirm(`Are you sure you want to ${statusText} this subscription plan?`)) {
        // Show loading state
        const originalContent = button.innerHTML;
        button.innerHTML = '<span class="flex items-center"><span class="animate-spin w-2 h-2 border border-gray-400 border-t-transparent rounded-full mr-2"></span>Updating...</span>';
        button.disabled = true;
        button.classList.add('opacity-50', 'cursor-not-allowed');

        fetch(`/admin/subscription-plans/${planId}`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_enabled: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showNotification('Plan status updated successfully!', 'success');
                // Reload the plans table
                setTimeout(() => {
                    loadPlans();
                }, 500);
            } else {
                // Restore button state on error
                button.innerHTML = originalContent;
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                showNotification(data.message || 'Failed to update plan status', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating plan status:', error);
            // Restore button state on error
            button.innerHTML = originalContent;
            button.disabled = false;
            button.classList.remove('opacity-50', 'cursor-not-allowed');
            showNotification('Failed to update plan status', 'error');
        });
    }
}

function editPlan(planId) {
    editingPlanId = planId;
    document.getElementById('modal-title').textContent = 'Edit Subscription Plan';

    // Fetch plan details and populate form
    fetch(`/admin/subscription-plans/${planId}`, {
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const plan = data.data;
            document.getElementById('plan-name').value = plan.name;
            document.getElementById('plan-duration').value = plan.duration_days;
            document.getElementById('plan-price').value = plan.price;
            document.getElementById('plan-description').value = plan.description || '';
            document.getElementById('plan-enabled').checked = plan.is_enabled;
            document.getElementById('plan-modal').classList.remove('hidden');
        }
    });
}

function closePlanModal() {
    document.getElementById('plan-modal').classList.add('hidden');
    editingPlanId = null;
}

function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this subscription plan?')) {
        fetch(`/admin/subscription-plans/${planId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Plan deleted successfully!', 'success');
                setTimeout(() => {
                    loadPlans();
                }, 500);
            } else {
                showNotification(data.message || 'Failed to delete plan', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting plan:', error);
            showNotification('Failed to delete plan', 'error');
        });
    }
}

// Handle form submission
document.getElementById('plan-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    data.is_enabled = document.getElementById('plan-enabled').checked;

    const url = editingPlanId ? `/admin/subscription-plans/${editingPlanId}` : '/admin/subscription-plans';
    const method = editingPlanId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const action = editingPlanId ? 'updated' : 'created';
            showNotification(`Plan ${action} successfully!`, 'success');
            closePlanModal();
            setTimeout(() => {
                loadPlans();
            }, 500);
        } else {
            showNotification(data.message || `Failed to ${editingPlanId ? 'update' : 'create'} plan`, 'error');
        }
    })
    .catch(error => {
        console.error('Error saving plan:', error);
        showNotification(`Failed to ${editingPlanId ? 'update' : 'create'} plan`, 'error');
    });
});

// Load plans when subscriptions tab is shown
document.addEventListener('DOMContentLoaded', function() {
    // Load plans initially if subscriptions tab is active
    if (!document.getElementById('subscriptions-content').classList.contains('hidden')) {
        loadPlans();
    }
});

// Override showTab to load plans when subscriptions tab is selected
const originalShowTab = showTab;
showTab = function(tabName) {
    originalShowTab(tabName);
    if (tabName === 'subscriptions') {
        // Add a small delay to ensure the tab content is visible
        setTimeout(() => {
            loadPlans();
        }, 100);
    }
};

// Auto-refresh plans every 30 seconds when on subscriptions tab
let refreshInterval;
function startAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    refreshInterval = setInterval(() => {
        if (!document.getElementById('subscriptions-content').classList.contains('hidden')) {
            loadPlans();
        }
    }, 30000); // 30 seconds
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});
</script>

<style>
.tab-button {
    @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
}

.tab-button.active {
    @apply border-indigo-500 text-indigo-600;
}
</style>
@endsection
