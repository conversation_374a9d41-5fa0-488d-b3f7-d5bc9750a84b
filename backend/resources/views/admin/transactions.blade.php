@extends('admin.layout')

@section('title', 'Transactions')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Transaction Management</h1>
                <p class="text-gray-600 mt-1">Monitor payments, revenue, and subscription transactions</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.settings') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Settings
                </a>
                <button onclick="loadTransactions()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Refresh
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm">Total Transactions</p>
                        <p class="text-2xl font-bold" id="totalTransactions">-</p>
                    </div>
                    <div class="text-3xl opacity-80">📊</div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm">Completed</p>
                        <p class="text-2xl font-bold" id="completedTransactions">-</p>
                    </div>
                    <div class="text-3xl opacity-80">✅</div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm">Total Revenue</p>
                        <p class="text-2xl font-bold" id="totalRevenue">-</p>
                    </div>
                    <div class="text-3xl opacity-80">💰</div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-6 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-orange-100 text-sm">Today's Revenue</p>
                        <p class="text-2xl font-bold" id="todayRevenue">-</p>
                    </div>
                    <div class="text-3xl opacity-80">📈</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <div class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                
                <div class="flex-1 min-w-48">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" id="searchInput" placeholder="Restaurant, Plan, Transaction ID..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="flex gap-2">
                    <button onclick="loadTransactions(1)" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
                        Filter
                    </button>
                    <button onclick="clearFilters()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors">
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Recent Transactions</h3>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState" class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading transactions...</p>
            </div>
            
            <!-- Error State -->
            <div id="errorState" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md m-4">
                <p>Failed to load transactions. Please try again.</p>
            </div>
            
            <!-- Table Container -->
            <div id="tableContainer" class="hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sr. No.</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Restaurant</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Table rows will be inserted here -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </button>
                            <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700" id="paginationInfo">
                                    <!-- Pagination info will be inserted here -->
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="paginationButtons">
                                    <!-- Pagination buttons will be inserted here -->
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="fixed top-4 right-4 z-50"></div>

<script>
let currentPage = 1;
let totalPages = 1;

// Notification system
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    
    // Safety check - if container doesn't exist, create it
    if (!container) {
        console.error('Notification container not found, creating one...');
        const newContainer = document.createElement('div');
        newContainer.id = 'notification-container';
        newContainer.className = 'fixed top-4 right-4 z-50';
        document.body.appendChild(newContainer);
        return showNotification(message, type); // Retry with new container
    }
    
    const notification = document.createElement('div');

    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    notification.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-2 transform transition-all duration-300 translate-x-full opacity-0`;
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    container.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Load statistics
async function loadStats() {
    try {
        const response = await fetch('/admin/transactions/stats', {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });
        const data = await response.json();
        
        if (data.success) {
            const stats = data.data;
            document.getElementById('totalTransactions').textContent = stats.total_transactions || 0;
            document.getElementById('completedTransactions').textContent = stats.completed_transactions || 0;
            document.getElementById('totalRevenue').textContent = `₹${(stats.total_revenue || 0).toLocaleString()}`;
            document.getElementById('todayRevenue').textContent = `₹${(stats.today_revenue || 0).toLocaleString()}`;
        }
    } catch (error) {
        console.error('Error loading stats:', error);
        showNotification('Failed to load statistics', 'error');
    }
}

// Load transactions
async function loadTransactions(page = 1) {
    const loadingState = document.getElementById('loadingState');
    const errorState = document.getElementById('errorState');
    const tableContainer = document.getElementById('tableContainer');
    
    loadingState.style.display = 'block';
    errorState.style.display = 'none';
    tableContainer.style.display = 'none';
    
    try {
        const status = document.getElementById('statusFilter').value;
        const search = document.getElementById('searchInput').value;
        
        const params = new URLSearchParams({
            page: page.toString(),
            per_page: '15'
        });
        
        if (status) params.append('status', status);
        if (search) params.append('search', search);
        
        const response = await fetch(`/admin/transactions/data?${params}`, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });
        const data = await response.json();
        
        if (data.success) {
            displayTransactions(data.data);
            updatePagination(data.pagination);
            currentPage = page;
            
            loadingState.style.display = 'none';
            tableContainer.style.display = 'block';
        } else {
            throw new Error(data.message || 'Failed to load transactions');
        }
    } catch (error) {
        console.error('Error loading transactions:', error);
        loadingState.style.display = 'none';
        errorState.style.display = 'block';
        errorState.textContent = error.message || 'Failed to load transactions. Please try again.';
        showNotification('Failed to load transactions', 'error');
    }
}

// Display transactions in table
function displayTransactions(transactions) {
    const tbody = document.getElementById('transactionsTableBody');
    tbody.innerHTML = '';
    
    if (transactions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-12 text-center text-gray-500">No transactions found</td></tr>';
        return;
    }
    
    transactions.forEach((transaction, index) => {
        const row = document.createElement('tr');
        const serialNumber = ((currentPage - 1) * 15) + index + 1;
        
        // Status badge styling
        let statusClass = 'px-2 py-1 text-xs font-semibold rounded-full ';
        switch(transaction.status) {
            case 'completed':
                statusClass += 'bg-green-100 text-green-800';
                break;
            case 'pending':
                statusClass += 'bg-yellow-100 text-yellow-800';
                break;
            case 'failed':
                statusClass += 'bg-red-100 text-red-800';
                break;
            default:
                statusClass += 'bg-gray-100 text-gray-800';
        }
        
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${serialNumber}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.restaurant_name || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.plan_name || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">₹${parseFloat(transaction.amount || 0).toLocaleString()}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="${statusClass}">${transaction.status}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span class="font-mono text-xs">${transaction.razorpay_payment_id || transaction.transaction_id || '-'}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${transaction.payment_date ? new Date(transaction.payment_date).toLocaleDateString() : '-'}
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Update pagination
function updatePagination(pagination) {
    totalPages = pagination.last_page;
    
    // Update pagination info
    const paginationInfo = document.getElementById('paginationInfo');
    paginationInfo.textContent = `Showing ${pagination.from || 0} to ${pagination.to || 0} of ${pagination.total || 0} results`;
    
    // Update pagination buttons
    const paginationButtons = document.getElementById('paginationButtons');
    let buttonsHTML = '';
    
    // Previous button
    buttonsHTML += `
        <button onclick="loadTransactions(${currentPage - 1})" 
                ${currentPage <= 1 ? 'disabled' : ''} 
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : ''}">
            Previous
        </button>
    `;
    
    // Page numbers
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        buttonsHTML += `
            <button onclick="loadTransactions(${i})" 
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${i === currentPage ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50'}">
                ${i}
            </button>
        `;
    }
    
    // Next button
    buttonsHTML += `
        <button onclick="loadTransactions(${currentPage + 1})" 
                ${currentPage >= totalPages ? 'disabled' : ''} 
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : ''}">
            Next
        </button>
    `;
    
    paginationButtons.innerHTML = buttonsHTML;
}

// Clear filters
function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('searchInput').value = '';
    loadTransactions(1);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadTransactions();
    
    // Add enter key support for search
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loadTransactions(1);
        }
    });
});
</script>
@endsection
