{"name": "frontend", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "11.4.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "axios": "^1.11.0", "expo": "~53.0.20", "expo-auth-session": "~6.2.1", "expo-crypto": "~14.1.5", "expo-image-picker": "~16.1.4", "expo-location": "^18.1.6", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.2", "react-native-google-places-autocomplete": "^2.5.7", "react-native-paper": "^5.14.5", "react-native-razorpay": "^2.3.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}