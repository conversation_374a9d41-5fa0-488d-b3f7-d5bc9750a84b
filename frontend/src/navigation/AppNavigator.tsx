import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { SplashScreen } from '../screens/SplashScreen';
import { LoginScreen } from '../screens/LoginScreen';
import { DashboardScreen } from '../screens/DashboardScreen';
import { AddUserScreen } from '../screens/AddUserScreen';
import { EditUserScreen } from '../screens/EditUserScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import PinSetupScreen from '../screens/PinSetupScreen';
import PinLoginScreen from '../screens/PinLoginScreen';
import AdminLoginScreen from '../screens/AdminLoginScreen';
import AdminDashboardScreen from '../screens/AdminDashboardScreen';
import ProfileScreen from '../screens/ProfileScreen';
import { RestaurantListScreen } from '../screens/RestaurantListScreen';
import { RestaurantProfileScreen } from '../screens/RestaurantProfileScreen';
import { SubscriptionPlansScreen } from '../screens/SubscriptionPlansScreen';
import { SubscriptionStatusScreen } from '../screens/SubscriptionStatusScreen';
import { TabNavigator } from './TabNavigator';
import { RestaurantUser } from '../types';

type Screen = 'Splash' | 'Login' | 'Main' | 'AddUser' | 'EditUser' | 'Settings' | 'PinSetup' | 'PinLogin' | 'AdminLogin' | 'AdminDashboard' | 'Profile' | 'RestaurantList' | 'RestaurantProfile' | 'SubscriptionPlans' | 'SubscriptionStatus';

export function AppNavigator() {
  const { isAuthenticated } = useAuth();
  const [currentScreen, setCurrentScreen] = useState<Screen>('Splash');
  const [editUser, setEditUser] = useState<RestaurantUser | null>(null);
  const [previousScreen, setPreviousScreen] = useState<Screen>('Main');

  // Monitor authentication state and redirect appropriately
  useEffect(() => {
    if (!isAuthenticated && currentScreen !== 'Splash' && currentScreen !== 'Login' && currentScreen !== 'PinLogin' && currentScreen !== 'AdminLogin' && currentScreen !== 'RestaurantList') {
      setCurrentScreen('RestaurantList'); // Show restaurant list for non-authenticated users
    }
  }, [isAuthenticated, currentScreen]);

  const renderScreen = () => {
    switch (currentScreen) {
      case 'Splash':
        return (
          <SplashScreen
            onNavigate={(screen) => setCurrentScreen(screen as Screen)}
          />
        );
      
      case 'Login':
        return (
          <LoginScreen
            onNavigate={(screen) => setCurrentScreen(screen as Screen)}
          />
        );
      
      case 'Main':
        return (
          <TabNavigator
            onNavigateToAddUser={() => setCurrentScreen('AddUser')}
            onNavigateToEditUser={(user) => {
              setEditUser(user);
              setCurrentScreen('EditUser');
            }}
            onNavigateToLogin={() => setCurrentScreen('Login')}
            onNavigateToProfile={() => {
              setPreviousScreen('Main');
              setCurrentScreen('Profile');
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
            onNavigateToRestaurantProfile={() => setCurrentScreen('RestaurantProfile')}
            onNavigateToSettings={() => setCurrentScreen('Settings')}
            onNavigateToSubscriptionPlans={() => {
              setPreviousScreen('Main');
              setCurrentScreen('SubscriptionPlans');
            }}
            onNavigateToSubscriptionStatus={() => {
              setPreviousScreen('Main');
              setCurrentScreen('SubscriptionStatus');
            }}
          />
        );
      
      case 'AddUser':
        return (
          <AddUserScreen
            onNavigateBack={() => setCurrentScreen('Main')}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
            onNavigateToHome={() => setCurrentScreen('Main')}
            onNavigateToSettings={() => setCurrentScreen('Settings')}
            onNavigateToSubscriptionPlans={() => {
              setPreviousScreen('AddUser');
              setCurrentScreen('SubscriptionPlans');
            }}
          />
        );

      case 'EditUser':
        if (!editUser) {
          // If no user is selected, go back to main
          setCurrentScreen('Main');
          return null;
        }
        return (
          <EditUserScreen
            route={{ params: { user: editUser } }}
            navigation={{
              goBack: () => {
                setEditUser(null);
                setCurrentScreen('Main');
              },
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
          />
        );

      case 'PinSetup':
        return (
          <PinSetupScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
          />
        );

      case 'PinLogin':
        return (
          <PinLoginScreen
            navigation={{
              navigate: (screen: string) => setCurrentScreen(screen as Screen),
              goBack: () => setCurrentScreen('Login'),
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
          />
        );

      case 'AdminLogin':
        return (
          <AdminLoginScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
              goBack: () => setCurrentScreen('Login'),
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
          />
        );

      case 'AdminDashboard':
        return (
          <AdminDashboardScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
          />
        );

      case 'Settings':
        return (
          <SettingsScreen
            onNavigateToLogin={() => setCurrentScreen('Login')}
            onNavigateToProfile={() => {
              setPreviousScreen('Settings');
              setCurrentScreen('Profile');
            }}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
            onNavigateToHome={() => setCurrentScreen('Main')}
            onNavigateToAddPerson={() => setCurrentScreen('AddUser')}
            onNavigateToSubscriptionPlans={() => {
              setPreviousScreen('Settings');
              setCurrentScreen('SubscriptionPlans');
            }}
            onNavigateToSubscriptionStatus={() => {
              setPreviousScreen('Settings');
              setCurrentScreen('SubscriptionStatus');
            }}
            isActive={currentScreen === 'Settings'}
          />
        );

      case 'Profile':
        return (
          <ProfileScreen
            onNavigateBack={() => setCurrentScreen(previousScreen)}
            onNavigateToLogin={() => setCurrentScreen('Login')}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
            onNavigateToHome={() => setCurrentScreen('Main')}
            onNavigateToAddPerson={() => setCurrentScreen('AddUser')}
            onNavigateToSettings={() => setCurrentScreen('Settings')}
          />
        );

      case 'RestaurantList':
        return (
          <RestaurantListScreen
            onNavigateToLogin={() => setCurrentScreen('Login')}
            onNavigateToAddPerson={() => setCurrentScreen('AddUser')}
            onNavigateToSettings={() => setCurrentScreen('Settings')}
            onNavigateToHome={() => setCurrentScreen('Main')}
          />
        );

      case 'RestaurantProfile':
        return (
          <RestaurantProfileScreen
            onNavigateBack={() => setCurrentScreen('Main')}
            onNavigateToRestaurantList={() => setCurrentScreen('RestaurantList')}
          />
        );

      case 'SubscriptionPlans':
        return (
          <SubscriptionPlansScreen
            onNavigateBack={() => setCurrentScreen(previousScreen)}
            onNavigateToStatus={() => setCurrentScreen('SubscriptionStatus')}
          />
        );

      case 'SubscriptionStatus':
        return (
          <SubscriptionStatusScreen
            onNavigateBack={() => setCurrentScreen(previousScreen)}
            onNavigateToPlans={() => setCurrentScreen('SubscriptionPlans')}
          />
        );

      default:
        return (
          <SplashScreen
            onNavigate={(screen) => setCurrentScreen(screen)}
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      {renderScreen()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
