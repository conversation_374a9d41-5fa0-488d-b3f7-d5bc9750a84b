import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { subscriptionService } from '../services/subscriptionService';
import { SubscriptionPlan, SubscriptionStatus } from '../types';
import { Card } from './Card';
import { Button } from './Button';

interface PremiumFeatureLockProps {
  featureName: string;
  description: string;
  onNavigateToPlans: () => void;
  children?: React.ReactNode;
}

export function PremiumFeatureLock({ 
  featureName, 
  description, 
  onNavigateToPlans,
  children 
}: PremiumFeatureLockProps) {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      const [statusData, plansData] = await Promise.all([
        subscriptionService.getSubscriptionStatus(),
        subscriptionService.getSubscriptionPlans(),
      ]);
      setSubscriptionStatus(statusData);
      setAvailablePlans(plansData);
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  // If loading, show loading state
  if (loading) {
    return (
      <View style={styles.container}>
        <Card style={styles.lockCard}>
          <ActivityIndicator size="large" color="#FF6B00" />
          <Text style={styles.loadingText}>Checking subscription status...</Text>
        </Card>
      </View>
    );
  }

  // If user has premium access, render children (the actual feature)
  if (subscriptionStatus?.has_premium_access) {
    return <>{children}</>;
  }

  // If no subscription plans are available, show that premium features are free
  if (availablePlans.length === 0) {
    return <>{children}</>;
  }

  // Show premium lock screen
  return (
    <View style={styles.container}>
      <Card style={styles.lockCard}>
        <View style={styles.lockIcon}>
          <Text style={styles.lockEmoji}>🔒</Text>
        </View>
        
        <Text style={styles.featureTitle}>{featureName}</Text>
        <Text style={styles.featureDescription}>{description}</Text>
        
        <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>Premium Features Include:</Text>
          <Text style={styles.benefitItem}>• Person Count Management</Text>
          <Text style={styles.benefitItem}>• Waiting List Management</Text>
          <Text style={styles.benefitItem}>• Real-time Status Updates</Text>
          <Text style={styles.benefitItem}>• Advanced Search & Filters</Text>
        </View>

        <Button
          title="View Subscription Plans"
          onPress={onNavigateToPlans}
          style={styles.upgradeButton}
        />

        {availablePlans.length > 0 && (
          <View style={styles.plansPreview}>
            <Text style={styles.plansPreviewTitle}>Available Plans:</Text>
            {availablePlans.slice(0, 2).map((plan) => {
              const priceFormatted = new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
              }).format(plan.price);

              return (
                <View key={plan.id} style={styles.planPreviewItem}>
                  <Text style={styles.planPreviewName}>{plan.name}</Text>
                  <Text style={styles.planPreviewPrice}>{priceFormatted}</Text>
                </View>
              );
            })}
            {availablePlans.length > 2 && (
              <Text style={styles.morePlansText}>
                +{availablePlans.length - 2} more plan{availablePlans.length > 3 ? 's' : ''}
              </Text>
            )}
          </View>
        )}
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  lockCard: {
    padding: 32,
    alignItems: 'center',
  },
  lockIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FEF3E2',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  lockEmoji: {
    fontSize: 40,
  },
  featureTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 12,
  },
  featureDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  benefitsContainer: {
    width: '100%',
    marginBottom: 24,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  benefitItem: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
    paddingLeft: 8,
  },
  upgradeButton: {
    width: '100%',
    marginBottom: 24,
  },
  plansPreview: {
    width: '100%',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
  },
  plansPreviewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  planPreviewItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  planPreviewName: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  planPreviewPrice: {
    fontSize: 14,
    color: '#FF6B00',
    fontWeight: '600',
  },
  morePlansText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
