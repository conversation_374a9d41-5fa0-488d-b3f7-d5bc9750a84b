import { Alert, Platform } from 'react-native';
import { SubscriptionPlan } from '../types';

// Razorpay module management with proper reference handling
class RazorpayModuleManager {
  private static instance: RazorpayModuleManager;
  private razorpayModule: any = null;
  private rawImport: any = null;
  private isInitialized: boolean = false;
  private initializationAttempted: boolean = false;

  static getInstance(): RazorpayModuleManager {
    if (!RazorpayModuleManager.instance) {
      RazorpayModuleManager.instance = new RazorpayModuleManager();
    }
    return RazorpayModuleManager.instance;
  }

  private constructor() {
    this.initializeModule();
  }

  private initializeModule(): void {
    if (this.initializationAttempted) {
      return;
    }

    this.initializationAttempted = true;
    console.log('🔄 Initializing Razorpay module...');
    console.log('📱 Platform:', Platform.OS);

    // Only try to load Razorpay on native platforms
    if (Platform.OS === 'android' || Platform.OS === 'ios') {
      try {
        // Import Razorpay module with more robust handling
        const RazorpayImport = require('react-native-razorpay');
        console.log('📦 Raw Razorpay import details:', {
          type: typeof RazorpayImport,
          keys: Object.keys(RazorpayImport || {}),
          hasDefault: !!(RazorpayImport && RazorpayImport.default),
          defaultType: RazorpayImport && RazorpayImport.default ? typeof RazorpayImport.default : 'undefined',
          defaultKeys: RazorpayImport && RazorpayImport.default ? Object.keys(RazorpayImport.default) : [],
          directOpen: !!(RazorpayImport && typeof RazorpayImport.open === 'function'),
          defaultOpen: !!(RazorpayImport && RazorpayImport.default && typeof RazorpayImport.default.open === 'function')
        });

        // Store the raw import for debugging
        this.rawImport = RazorpayImport;

        // Try different ways to access the module with more robust checks
        let moduleCandidate = null;

        if (RazorpayImport && typeof RazorpayImport.open === 'function') {
          moduleCandidate = RazorpayImport;
          console.log('✅ Using direct import (has open method)');
        } else if (RazorpayImport && RazorpayImport.default) {
          if (typeof RazorpayImport.default.open === 'function') {
            moduleCandidate = RazorpayImport.default;
            console.log('✅ Using default export (has open method)');
          } else if (typeof RazorpayImport.default === 'function') {
            // Sometimes the default export is the function itself
            moduleCandidate = RazorpayImport.default;
            console.log('✅ Using default export as function');
          }
        }

        // Final validation of the module candidate
        if (moduleCandidate) {
          console.log('🔍 Module candidate validation:', {
            type: typeof moduleCandidate,
            hasOpen: typeof moduleCandidate.open === 'function',
            keys: Object.keys(moduleCandidate),
            isFunction: typeof moduleCandidate === 'function'
          });

          // Store the module directly without wrapper to prevent reference issues
          this.razorpayModule = moduleCandidate;
          this.isInitialized = true;
          console.log('✅ Razorpay module initialized directly');

          // Test the module immediately after initialization
          try {
            if (typeof this.razorpayModule.open === 'function') {
              console.log('✅ Module.open method confirmed working');
            } else if (typeof this.razorpayModule === 'function') {
              console.log('✅ Module as function confirmed working');
            }
          } catch (testError) {
            console.warn('⚠️ Module test failed:', testError);
          }
        } else {
          console.warn('⚠️ No valid Razorpay module found');
          this.razorpayModule = null;
          this.isInitialized = false;
        }
      } catch (error) {
        console.error('❌ Failed to load Razorpay module:', error);
        this.razorpayModule = null;
        this.isInitialized = false;
      }
    } else {
      console.log('ℹ️ Web platform detected - Razorpay not available');
      this.razorpayModule = null;
      this.isInitialized = false;
    }
  }

  public getModule(): any {
    if (!this.initializationAttempted) {
      this.initializeModule();
    }

    // Return the module directly
    return this.razorpayModule;
  }

  public getOpenFunction(): any {
    if (!this.initializationAttempted) {
      this.initializeModule();
    }

    if (this.razorpayModule) {
      if (typeof this.razorpayModule.open === 'function') {
        console.log('🔍 Returning module.open function');
        return this.razorpayModule.open;
      } else if (typeof this.razorpayModule === 'function') {
        console.log('🔍 Returning module as function');
        return this.razorpayModule;
      }
    }

    return null;
  }

  public isAvailable(): boolean {
    const module = this.getModule();
    const available = !!(module && (typeof module.open === 'function' || typeof module === 'function'));

    console.log('🔍 Razorpay availability check:', {
      moduleExists: !!module,
      moduleType: typeof module,
      hasOpenMethod: module && typeof module.open === 'function',
      moduleIsFunction: typeof module === 'function',
      isInitialized: this.isInitialized,
      available: available
    });
    return available;
  }

  public forceReinitialize(): boolean {
    console.log('🔄 Force reinitializing Razorpay...');
    this.initializationAttempted = false;
    this.isInitialized = false;
    this.razorpayModule = null;
    this.rawImport = null;
    this.initializeModule();
    return this.isAvailable();
  }

  public getWrapperInfo(): any {
    return this.razorpayModule ? {
      hasModule: true,
      moduleType: typeof this.razorpayModule,
      hasOpenMethod: typeof this.razorpayModule.open === 'function',
      moduleIsFunction: typeof this.razorpayModule === 'function'
    } : { hasModule: false };
  }

  public getRawImportInfo(): any {
    return this.rawImport ? {
      type: typeof this.rawImport,
      keys: Object.keys(this.rawImport),
      hasDefault: !!this.rawImport.default
    } : null;
  }
}

// Global instance
const razorpayManager = RazorpayModuleManager.getInstance();

export interface RazorpayOptions {
  description: string;
  image?: string;
  currency: string;
  key: string;
  amount: number;
  name: string;
  order_id?: string;
  prefill: {
    email: string;
    contact: string;
    name: string;
  };
  theme: {
    color: string;
  };
}

export interface PaymentResult {
  razorpay_payment_id: string;
  razorpay_order_id?: string;
  razorpay_signature?: string;
}

export interface PaymentError {
  code: string;
  description: string;
  source: string;
  step: string;
  reason: string;
  metadata: any;
}

class RazorpayService {
  private static instance: RazorpayService;
  private readonly testKeyId = 'rzp_test_Go3jN8rdNmRJ7P';
  private readonly testKeySecret = 'sbD3JVTl7W7UJ18O43cRmtCE';

  public static getInstance(): RazorpayService {
    if (!RazorpayService.instance) {
      RazorpayService.instance = new RazorpayService();
    }
    return RazorpayService.instance;
  }

  /**
   * Check if Razorpay is available
   */
  isRazorpayAvailable(): boolean {
    return razorpayManager.isAvailable();
  }

  /**
   * Get Razorpay module with runtime validation
   */
  private getRazorpayModule(): any {
    const module = razorpayManager.getModule();
    if (!module) {
      console.log('⚠️ Razorpay module not available, attempting reinitialize...');
      razorpayManager.forceReinitialize();
      return razorpayManager.getModule();
    }
    return module;
  }

  /**
   * Test Razorpay module functionality
   */
  public testRazorpayModule(): { success: boolean; details: any } {
    try {
      const module = this.getRazorpayModule();
      const openFunction = razorpayManager.getOpenFunction();
      const isAvailable = razorpayManager.isAvailable();

      const result = {
        success: isAvailable && !!openFunction && typeof openFunction === 'function',
        details: {
          moduleExists: !!module,
          moduleType: typeof module,
          openFunctionExists: !!openFunction,
          openFunctionType: typeof openFunction,
          hasOpenMethod: module && typeof module.open === 'function',
          platform: Platform.OS,
          moduleKeys: module ? Object.keys(module) : [],
          isManagerAvailable: isAvailable,
          wrapperInfo: razorpayManager.getWrapperInfo(),
          rawImportInfo: razorpayManager.getRawImportInfo()
        }
      };

      console.log('🧪 Comprehensive Razorpay module test result:', result);
      return result;
    } catch (error) {
      console.error('❌ Razorpay module test failed:', error);
      return {
        success: false,
        details: { error: error.message, platform: Platform.OS }
      };
    }
  }

  /**
   * Initialize payment for subscription plan
   */
  async initiatePayment(
    plan: SubscriptionPlan,
    userDetails: {
      name: string;
      email: string;
      contact: string;
    }
  ): Promise<PaymentResult> {
    try {
      // Pre-payment module test
      console.log('🔍 Pre-payment Razorpay module test...');
      const moduleTest = this.testRazorpayModule();
      if (!moduleTest.success) {
        console.warn('⚠️ Razorpay module test failed, will use simulation mode');
      }
      const options: RazorpayOptions = {
        description: `${plan.name} - ${plan.formatted_duration || plan.duration_days + ' days'}`,
        image: 'https://your-logo-url.com/logo.png', // Replace with your app logo
        currency: 'INR',
        key: this.testKeyId,
        amount: Math.round(plan.price * 100), // Amount in paise (multiply by 100)
        name: 'Waiting List App',
        prefill: {
          email: userDetails.email,
          contact: userDetails.contact,
          name: userDetails.name,
        },
        theme: {
          color: '#FF6B00', // Your app's primary color
        },
      };

      console.log('🔄 Initiating Razorpay payment with options:', {
        ...options,
        key: 'rzp_test_***', // Hide key in logs
      });

      let data;

      // Get Razorpay module and open function with validation
      const RazorpayModule = this.getRazorpayModule();
      const openFunction = razorpayManager.getOpenFunction();

      console.log('🔍 Payment initiation - Razorpay status:', {
        moduleAvailable: !!RazorpayModule,
        moduleType: typeof RazorpayModule,
        openFunctionAvailable: !!openFunction,
        openFunctionType: typeof openFunction,
        hasOpenMethod: RazorpayModule && typeof RazorpayModule.open === 'function',
        platform: Platform.OS,
        managerAvailable: razorpayManager.isAvailable()
      });

      if (openFunction && typeof openFunction === 'function') {
        // Real Razorpay integration
        console.log('🔄 Using real Razorpay integration');
        console.log('💳 Opening Razorpay with options:', {
          amount: options.amount,
          currency: options.currency,
          description: options.description,
          key: 'rzp_test_***'
        });

        try {
          // CRITICAL FIX: Capture function reference at validation time to prevent null reference
          let paymentFunction = null;
          let callMethod = '';

          // Get fresh module reference
          const freshModule = razorpayManager.getModule();

          console.log('🔍 Final validation and function capture:', {
            moduleExists: !!freshModule,
            moduleType: typeof freshModule,
            moduleHasOpen: freshModule && typeof freshModule.open === 'function',
            moduleIsFunction: typeof freshModule === 'function',
            moduleKeys: freshModule ? Object.keys(freshModule) : [],
            timestamp: Date.now()
          });

          // Capture the function reference immediately during validation
          if (freshModule && typeof freshModule.open === 'function') {
            // Create a wrapper function to prevent context loss
            paymentFunction = (options) => {
              console.log('🔥 Executing wrapped module.open call');
              return freshModule.open(options);
            };
            callMethod = 'wrapped module.open()';
            console.log('✅ Created wrapped module.open function');
          } else if (freshModule && typeof freshModule === 'function') {
            // Create a wrapper for direct module call
            paymentFunction = (options) => {
              console.log('🔥 Executing wrapped module call');
              return freshModule(options);
            };
            callMethod = 'wrapped module function';
            console.log('✅ Created wrapped module function');
          } else {
            // Last resort: re-require and capture immediately
            console.log('🔄 Last resort: re-requiring and capturing...');
            try {
              const lastResortModule = require('react-native-razorpay');
              console.log('🔍 Last resort module structure:', {
                type: typeof lastResortModule,
                keys: Object.keys(lastResortModule || {}),
                hasDefault: !!lastResortModule.default,
                defaultType: lastResortModule.default ? typeof lastResortModule.default : 'undefined'
              });

              if (lastResortModule && lastResortModule.default && typeof lastResortModule.default.open === 'function') {
                paymentFunction = (options) => {
                  console.log('🔥 Executing last resort wrapped call');
                  return lastResortModule.default.open(options);
                };
                callMethod = 'wrapped last resort module.open()';
                console.log('✅ Created wrapped last resort function');
              }
            } catch (requireError) {
              console.error('❌ Failed to re-require Razorpay:', requireError);
            }
          }

          // Validate captured function
          if (!paymentFunction || typeof paymentFunction !== 'function') {
            throw new Error(`Failed to capture valid payment function. Method: ${callMethod}, Type: ${typeof paymentFunction}`);
          }

          console.log(`🚀 Calling captured ${callMethod}...`);
          console.log('🔍 About to call captured function with options:', typeof options);

          // Call the captured function (this should prevent null reference)
          data = await paymentFunction(options);

          console.log('✅ Razorpay payment completed successfully');
        } catch (error: any) {
          console.error('❌ Razorpay payment failed:', error);
          console.error('❌ Error details:', {
            message: error.message,
            code: error.code,
            description: error.description,
            stack: error.stack
          });

          // Handle different types of Razorpay errors
          if (error.code === 'PAYMENT_CANCELLED') {
            throw new Error('Payment was cancelled by user');
          } else if (error.code === 'NETWORK_ERROR') {
            throw new Error('Network error. Please check your internet connection and try again.');
          } else if (error.code === 'INVALID_OPTIONS') {
            throw new Error('Payment configuration error. Please contact support.');
          } else {
            throw new Error(error.description || error.message || 'Payment failed. Please try again.');
          }
        }
      } else {
        // Fallback simulation for development/testing
        console.log('🔄 Using Razorpay simulation mode');
        console.log('ℹ️ Reason: Razorpay module not available or open method not found');
        console.log('ℹ️ Platform:', Platform.OS);

        const isWeb = Platform.OS === 'web';
        const message = isWeb
          ? 'Razorpay is not available on web platform. Using simulation mode for testing.'
          : 'Razorpay is not available in this environment. Using simulation mode for testing.';

        // Show user-friendly message about simulation
        Alert.alert(
          'Payment Simulation',
          message,
          [
            { text: 'Cancel', style: 'cancel', onPress: () => { throw new Error('Payment cancelled by user'); } },
            { text: 'Continue with Simulation', style: 'default' }
          ]
        );

        // Simulate successful payment
        data = {
          razorpay_payment_id: `pay_sim_${Date.now()}`,
          razorpay_order_id: `order_sim_${Date.now()}`,
          razorpay_signature: `sig_sim_${Date.now()}`,
        };

        // Add a small delay to simulate processing
        await new Promise(resolve => setTimeout(resolve, 1500));
        console.log('✅ Payment simulation completed successfully');
      }

      const isSimulated = data.razorpay_payment_id.includes('_sim_');
      console.log(`✅ Payment successful ${isSimulated ? '(SIMULATED)' : '(REAL)'}:`, {
        payment_id: data.razorpay_payment_id,
        order_id: data.razorpay_order_id,
      });

      return data;
    } catch (error: any) {
      console.error('❌ Payment failed:', error);

      if (error.code === 'payment_cancelled') {
        throw new Error('Payment was cancelled by user');
      } else if (error.code === 'payment_failed') {
        throw new Error(`Payment failed: ${error.description || 'Unknown error'}`);
      } else {
        throw new Error(`Payment error: ${error.description || error.message || 'Unknown error'}`);
      }
    }
  }

  /**
   * Verify payment on backend
   */
  async verifyPayment(
    paymentData: PaymentResult,
    planId: number
  ): Promise<any> {
    try {
      // This would typically call your backend to verify the payment
      // For now, we'll return the payment data for the subscription service
      return {
        payment_id: paymentData.razorpay_payment_id,
        order_id: paymentData.razorpay_order_id,
        signature: paymentData.razorpay_signature,
        plan_id: planId,
        payment_method: 'razorpay',
        status: 'completed',
      };
    } catch (error) {
      console.error('❌ Payment verification failed:', error);
      throw new Error('Payment verification failed');
    }
  }

  /**
   * Format amount for display
   */
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Get payment method info
   */
  getPaymentMethodInfo() {
    return {
      name: 'Razorpay',
      supportedMethods: ['UPI', 'Cards', 'Net Banking', 'Wallets'],
      testMode: true,
      keyId: this.testKeyId,
    };
  }

  /**
   * Show payment confirmation dialog
   */
  showPaymentConfirmation(
    plan: SubscriptionPlan,
    onConfirm: () => void,
    onCancel: () => void
  ) {
    const amount = this.formatAmount(plan.price);
    
    Alert.alert(
      'Confirm Payment',
      `You are about to purchase:\n\n${plan.name}\nDuration: ${plan.formatted_duration}\nAmount: ${amount}\n\nProceed with payment?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: onCancel,
        },
        {
          text: 'Pay Now',
          style: 'default',
          onPress: onConfirm,
        },
      ]
    );
  }

  /**
   * Show payment success dialog
   */
  showPaymentSuccess(
    plan: SubscriptionPlan,
    paymentId: string,
    onOk: () => void
  ) {
    Alert.alert(
      'Payment Successful! 🎉',
      `Your ${plan.name} subscription has been activated successfully.\n\nPayment ID: ${paymentId}\n\nYou now have access to premium features!`,
      [
        {
          text: 'OK',
          onPress: onOk,
        },
      ]
    );
  }

  /**
   * Show payment error dialog
   */
  showPaymentError(error: string, onRetry?: () => void) {
    const buttons = [
      {
        text: 'OK',
        style: 'cancel' as const,
      },
    ];

    if (onRetry) {
      buttons.unshift({
        text: 'Retry',
        style: 'default' as const,
        onPress: onRetry,
      });
    }

    Alert.alert(
      'Payment Failed',
      error,
      buttons
    );
  }
}

export const razorpayService = RazorpayService.getInstance();
