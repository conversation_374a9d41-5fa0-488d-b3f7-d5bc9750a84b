import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from './api';
import { razorpayService } from './razorpayService';
import {
  SubscriptionPlan,
  UserSubscription,
  SubscriptionStatus,
  PurchaseSubscriptionData
} from '../types';

class SubscriptionService {
  private static instance: SubscriptionService;
  private cachedPlans: SubscriptionPlan[] | null = null;
  private cachedStatus: SubscriptionStatus | null = null;
  private statusCacheTime: number = 0;
  private plansCacheTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  public static getInstance(): SubscriptionService {
    if (!SubscriptionService.instance) {
      SubscriptionService.instance = new SubscriptionService();
    }
    return SubscriptionService.instance;
  }

  /**
   * Get available subscription plans
   */
  async getSubscriptionPlans(forceRefresh: boolean = false): Promise<SubscriptionPlan[]> {
    try {
      // Return cached data if available and not expired
      if (!forceRefresh && this.cachedPlans && (Date.now() - this.plansCacheTime) < this.CACHE_DURATION) {
        return this.cachedPlans;
      }

      const response = await apiService.get('/subscription-plans');

      if (response.success) {
        this.cachedPlans = response.data;
        this.plansCacheTime = Date.now();
        
        // Cache in AsyncStorage for offline access
        await AsyncStorage.setItem('subscription_plans', JSON.stringify(response.data));
        
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch subscription plans');
      }
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      
      // Try to return cached data from AsyncStorage
      try {
        const cachedData = await AsyncStorage.getItem('subscription_plans');
        if (cachedData) {
          const plans = JSON.parse(cachedData);
          this.cachedPlans = plans;
          return plans;
        }
      } catch (cacheError) {
        console.error('Error reading cached subscription plans:', cacheError);
      }
      
      throw error;
    }
  }

  /**
   * Get user's subscription status
   */
  async getSubscriptionStatus(forceRefresh: boolean = false): Promise<SubscriptionStatus> {
    try {
      // Return cached data if available and not expired
      if (!forceRefresh && this.cachedStatus && (Date.now() - this.statusCacheTime) < this.CACHE_DURATION) {
        return this.cachedStatus;
      }

      const response = await apiService.get('/subscriptions/status');

      if (response.success) {
        this.cachedStatus = response.data;
        this.statusCacheTime = Date.now();
        
        // Cache in AsyncStorage
        await AsyncStorage.setItem('subscription_status', JSON.stringify(response.data));
        
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch subscription status');
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error);
      
      // Try to return cached data from AsyncStorage
      try {
        const cachedData = await AsyncStorage.getItem('subscription_status');
        if (cachedData) {
          const status = JSON.parse(cachedData);
          this.cachedStatus = status;
          return status;
        }
      } catch (cacheError) {
        console.error('Error reading cached subscription status:', cacheError);
      }
      
      throw error;
    }
  }

  /**
   * Purchase a subscription plan
   */
  async purchaseSubscription(data: PurchaseSubscriptionData): Promise<UserSubscription> {
    try {
      const response = await apiService.post('/subscriptions/purchase', data);

      if (response.success) {
        // Clear cached status to force refresh
        this.cachedStatus = null;
        this.statusCacheTime = 0;

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to purchase subscription');
      }
    } catch (error) {
      console.error('Error purchasing subscription:', error);
      throw error;
    }
  }

  /**
   * Purchase subscription with Razorpay payment
   */
  async purchaseWithRazorpay(
    plan: SubscriptionPlan,
    userDetails: {
      name: string;
      email: string;
      contact: string;
    }
  ): Promise<UserSubscription> {
    try {
      // Initiate Razorpay payment
      const paymentResult = await razorpayService.initiatePayment(plan, userDetails);

      // Verify payment and create subscription
      const verificationData = await razorpayService.verifyPayment(paymentResult, plan.id);

      // Create subscription with payment details
      const subscriptionData: PurchaseSubscriptionData = {
        subscription_plan_id: plan.id,
        payment_method: 'razorpay',
        transaction_id: paymentResult.razorpay_payment_id,
        payment_details: {
          razorpay_payment_id: paymentResult.razorpay_payment_id,
          razorpay_order_id: paymentResult.razorpay_order_id,
          razorpay_signature: paymentResult.razorpay_signature,
          amount_paid: plan.price,
          currency: 'INR',
        },
      };

      const response = await apiService.post('/subscriptions/purchase', subscriptionData);

      if (response.success) {
        // Clear cached status to force refresh
        this.cachedStatus = null;
        this.statusCacheTime = 0;

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create subscription after payment');
      }
    } catch (error) {
      console.error('Error purchasing subscription with Razorpay:', error);
      throw error;
    }
  }

  /**
   * Get user's subscription history
   */
  async getSubscriptionHistory(): Promise<UserSubscription[]> {
    try {
      const response = await apiService.get('/subscriptions');

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch subscription history');
      }
    } catch (error) {
      console.error('Error fetching subscription history:', error);
      throw error;
    }
  }

  /**
   * Cancel active subscription
   */
  async cancelSubscription(): Promise<UserSubscription> {
    try {
      const response = await apiService.post('/subscriptions/cancel');

      if (response.success) {
        // Clear cached status to force refresh
        this.cachedStatus = null;
        this.statusCacheTime = 0;

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Get user's transaction history
   */
  async getUserTransactions(): Promise<any[]> {
    try {
      const response = await apiService.get('/user-transactions');

      if (response.success) {
        return response.data || [];
      } else {
        throw new Error(response.message || 'Failed to fetch transactions');
      }
    } catch (error) {
      console.error('Error fetching user transactions:', error);
      return []; // Return empty array on error
    }
  }

  /**
   * Check if user has premium access (cached check)
   */
  async hasPremiumAccess(): Promise<boolean> {
    try {
      const status = await this.getSubscriptionStatus();
      return status.has_premium_access;
    } catch (error) {
      console.error('Error checking premium access:', error);
      // Default to false if we can't determine status
      return false;
    }
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.cachedPlans = null;
    this.cachedStatus = null;
    this.plansCacheTime = 0;
    this.statusCacheTime = 0;
  }

  /**
   * Clear cached data from AsyncStorage
   */
  async clearStoredCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove(['subscription_plans', 'subscription_status']);
    } catch (error) {
      console.error('Error clearing subscription cache:', error);
    }
  }
}

export const subscriptionService = SubscriptionService.getInstance();
