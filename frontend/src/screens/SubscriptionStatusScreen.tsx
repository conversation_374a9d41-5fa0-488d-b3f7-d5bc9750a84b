import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { subscriptionService } from '../services/subscriptionService';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { AppHeader } from '../components/AppHeader';
import { SubscriptionStatus, UserSubscription } from '../types';

interface SubscriptionStatusScreenProps {
  onNavigateBack: () => void;
  onNavigateToPlans: () => void;
}

interface Transaction {
  id: number;
  plan_name: string;
  amount: number;
  currency: string;
  payment_method: string;
  transaction_id: string;
  razorpay_payment_id?: string;
  status: 'completed' | 'pending' | 'failed';
  payment_date: string;
  created_at: string;
}

export function SubscriptionStatusScreen({
  onNavigateBack,
  onNavigateToPlans
}: SubscriptionStatusScreenProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [cancelling, setCancelling] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    subscriptionCard: {
      padding: theme.spacing.lg,
    },
    subscriptionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    planName: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      flex: 1,
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 20,
      marginLeft: theme.spacing.sm,
    },
    activeBadge: {
      backgroundColor: '#10B981',
    },
    inactiveBadge: {
      backgroundColor: '#EF4444',
    },
    statusText: {
      color: 'white',
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
    },
    subscriptionDetails: {
      marginBottom: theme.spacing.md,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.sm,
    },
    detailLabel: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
    },
    detailValue: {
      fontSize: theme.typography.fontSize.md,
      fontWeight: '500',
      color: theme.colors.text,
    },
    transactionItem: {
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    transactionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    transactionPlan: {
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    transactionAmount: {
      fontSize: theme.typography.fontSize.md,
      fontWeight: 'bold',
      color: '#10B981',
    },
    transactionDetails: {
      marginTop: theme.spacing.sm,
    },
    transactionRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.xs,
    },
    transactionLabel: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    transactionValue: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.text,
    },
    emptyState: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.md,
      marginTop: theme.spacing.xl,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: theme.spacing.md,
      marginTop: theme.spacing.md,
    },
  });

  useEffect(() => {
    loadData();
  }, [loadData]);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Load subscription status
      const status = await subscriptionService.getSubscriptionStatus();
      setSubscriptionStatus(status);

      // Load user's transactions
      const userTransactions = await subscriptionService.getUserTransactions();
      setTransactions(userTransactions);

    } catch (error) {
      console.error('Error loading subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription data. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  const handleCancelSubscription = async () => {
    if (!subscriptionStatus?.active_subscription) return;

    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will lose access to premium features.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel Subscription',
          style: 'destructive',
          onPress: async () => {
            try {
              setCancelling(true);
              await subscriptionService.cancelSubscription();
              
              Alert.alert(
                'Subscription Cancelled',
                'Your subscription has been cancelled successfully.',
                [{ text: 'OK', onPress: loadData }]
              );
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to cancel subscription');
            } finally {
              setCancelling(false);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10B981';
      case 'expired':
        return '#EF4444';
      case 'cancelled':
        return '#6B7280';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'expired':
        return 'Expired';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <AppHeader 
          title="Subscription Status" 
          showBackButton 
          onBackPress={onNavigateBack}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B00" />
          <Text style={styles.loadingText}>Loading subscription status...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AppHeader 
        title="Subscription Status" 
        showBackButton 
        onBackPress={onNavigateBack}
      />
      
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Current Status */}
        <Card style={styles.statusCard}>
          <Text style={styles.sectionTitle}>Current Status</Text>
          
          {subscriptionStatus?.has_premium_access ? (
            <View style={styles.activeStatus}>
              <Text style={styles.statusText}>✅ Premium Access Active</Text>
              
              {subscriptionStatus.active_subscription ? (
                <View style={styles.subscriptionDetails}>
                  <Text style={styles.detailText}>
                    Plan: {subscriptionStatus.active_subscription.subscription_plan?.name}
                  </Text>
                  <Text style={styles.detailText}>
                    Expires: {formatDate(subscriptionStatus.active_subscription.expires_at)}
                  </Text>
                  <Text style={styles.detailText}>
                    Days remaining: {subscriptionStatus.active_subscription.days_remaining}
                  </Text>
                  
                  <Button
                    title={cancelling ? 'Cancelling...' : 'Cancel Subscription'}
                    onPress={handleCancelSubscription}
                    disabled={cancelling}
                    style={[styles.cancelButton, { backgroundColor: '#EF4444' }]}
                  />
                </View>
              ) : (
                <Text style={styles.freeAccessText}>
                  Premium features are currently free for all users.
                </Text>
              )}
            </View>
          ) : (
            <View style={styles.inactiveStatus}>
              <Text style={styles.statusText}>❌ No Active Subscription</Text>
              <Text style={styles.inactiveText}>
                You need a subscription to access premium features.
              </Text>
              
              <Button
                title="View Subscription Plans"
                onPress={onNavigateToPlans}
                style={styles.upgradeButton}
              />
            </View>
          )}
        </Card>

        {/* Subscription History */}
        {transactions.length > 0 && (
          <Card style={styles.historyCard}>
            <Text style={styles.sectionTitle}>Subscription History</Text>

            {transactions.map((subscription) => (
              <View key={subscription.id} style={styles.historyItem}>
                <View style={styles.historyHeader}>
                  <Text style={styles.historyPlan}>
                    {subscription.subscription_plan?.name || subscription.plan_snapshot?.name}
                  </Text>
                  <View style={[
                    styles.statusBadge, 
                    { backgroundColor: getStatusColor(subscription.status) }
                  ]}>
                    <Text style={styles.statusBadgeText}>
                      {getStatusText(subscription.status)}
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.historyDetail}>
                  Duration: {subscription.subscription_plan?.formatted_duration || 
                           `${subscription.plan_snapshot?.duration_days} days`}
                </Text>
                <Text style={styles.historyDetail}>
                  Amount: {new Intl.NumberFormat('en-IN', {
                    style: 'currency',
                    currency: 'INR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  }).format(subscription.amount_paid)}
                </Text>
                <Text style={styles.historyDetail}>
                  Period: {formatDate(subscription.starts_at)} - {formatDate(subscription.expires_at)}
                </Text>
                
                {subscription.payment_method && (
                  <Text style={styles.historyDetail}>
                    Payment: {subscription.payment_method}
                  </Text>
                )}
              </View>
            ))}
          </Card>
        )}
      </ScrollView>
    </View>
  );
}


