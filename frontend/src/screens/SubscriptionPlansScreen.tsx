import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { subscriptionService } from '../services/subscriptionService';
import { razorpayService } from '../services/razorpayService';
import { SubscriptionPlan, SubscriptionStatus } from '../types';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { AppHeader } from '../components/AppHeader';
import { useAuth } from '../context/AuthContext';

interface SubscriptionPlansScreenProps {
  onNavigateBack: () => void;
  onNavigateToStatus: () => void;
}

export function SubscriptionPlansScreen({
  onNavigateBack,
  onNavigateToStatus
}: SubscriptionPlansScreenProps) {
  const { user } = useAuth();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [purchasing, setPurchasing] = useState<number | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [plansData, statusData] = await Promise.all([
        subscriptionService.getSubscriptionPlans(),
        subscriptionService.getSubscriptionStatus(),
      ]);
      setPlans(plansData);
      setSubscriptionStatus(statusData);
    } catch (error) {
      console.error('Error loading subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription plans. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      const [plansData, statusData] = await Promise.all([
        subscriptionService.getSubscriptionPlans(true),
        subscriptionService.getSubscriptionStatus(true),
      ]);
      setPlans(plansData);
      setSubscriptionStatus(statusData);
    } catch (error) {
      console.error('Error refreshing subscription data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handlePurchase = async (plan: SubscriptionPlan) => {
    try {
      setPurchasing(plan.id);

      if (!user) {
        Alert.alert('Error', 'Please login to purchase a subscription');
        return;
      }

      // Get user details for payment
      const userDetails = {
        name: user.name || 'User',
        email: user.email || '<EMAIL>',
        contact: user.mobile_number || '9999999999',
      };

      // Test Razorpay module before payment
      console.log('🧪 Testing Razorpay module before payment...');
      const razorpayTest = razorpayService.testRazorpayModule();
      console.log('🧪 Razorpay test result:', razorpayTest);

      if (!razorpayTest.success) {
        console.warn('⚠️ Razorpay test failed, but proceeding with payment attempt...');
      }

      // Directly process payment with Razorpay (no confirmation dialog)
      try {
        const subscription = await subscriptionService.purchaseWithRazorpay(plan, userDetails);

        // Show success message
        razorpayService.showPaymentSuccess(
          plan,
          subscription.id.toString(),
          () => {
            // Refresh data and navigate to status
            loadData();
            onNavigateToStatus();
          }
        );
      } catch (error: any) {
        console.error('Payment error:', error);
        razorpayService.showPaymentError(
          error.message || 'Payment failed. Please try again.',
          () => handlePurchase(plan) // Retry function
        );
      }
    } catch (error) {
      console.error('Error initiating purchase:', error);
      Alert.alert('Error', 'Failed to initiate payment. Please try again.');
    } finally {
      setPurchasing(null);
    }
  };

  const renderPlan = (plan: SubscriptionPlan) => {
    // Format duration for better display
    let durationDisplay = plan.formatted_duration;
    if (plan.duration_days === 30) {
      durationDisplay = '1 Month (30 days)';
    } else if (plan.duration_days === 365) {
      durationDisplay = '1 Year (365 days)';
    } else if (plan.duration_days === 7) {
      durationDisplay = '1 Week (7 days)';
    } else {
      durationDisplay = `${plan.duration_days} days`;
    }

    // Format price in INR
    const priceFormatted = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(plan.price);

    return (
      <Card key={plan.id} style={styles.planCard}>
        <View style={styles.planHeader}>
          <Text style={styles.planName}>{plan.name}</Text>
          <Text style={styles.planPrice}>{priceFormatted}</Text>
        </View>

        <Text style={styles.planDuration}>{durationDisplay}</Text>
      
      {plan.description && (
        <Text style={styles.planDescription}>{plan.description}</Text>
      )}

      {plan.features && Array.isArray(plan.features) && plan.features.length > 0 && (
        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>Features:</Text>
          {plan.features.map((feature, index) => (
            <Text key={index} style={styles.featureItem}>• {feature}</Text>
          ))}
        </View>
      )}

        <Button
          title={purchasing === plan.id ? 'Processing...' : 'Buy Now'}
          onPress={() => handlePurchase(plan)}
          disabled={purchasing === plan.id}
          style={styles.purchaseButton}
        />
      </Card>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <AppHeader 
          title="Subscription Plans" 
          showBackButton 
          onBackPress={onNavigateBack}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B00" />
          <Text style={styles.loadingText}>Loading subscription plans...</Text>
        </View>
      </View>
    );
  }

  // If user already has premium access, show status instead
  if (subscriptionStatus?.has_premium_access) {
    return (
      <View style={styles.container}>
        <AppHeader 
          title="Subscription Plans" 
          showBackButton 
          onBackPress={onNavigateBack}
        />
        <ScrollView 
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <Card style={styles.statusCard}>
            <Text style={styles.statusTitle}>✅ Premium Access Active</Text>
            {subscriptionStatus.active_subscription ? (
              <View>
                <Text style={styles.statusText}>
                  Plan: {subscriptionStatus.active_subscription.subscription_plan?.name}
                </Text>
                <Text style={styles.statusText}>
                  Days remaining: {subscriptionStatus.active_subscription.days_remaining}
                </Text>
                <Button
                  title="View Subscription Details"
                  onPress={onNavigateToStatus}
                  style={styles.statusButton}
                />
              </View>
            ) : (
              <Text style={styles.statusText}>
                Premium features are currently free for all users.
              </Text>
            )}
          </Card>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AppHeader 
        title="Subscription Plans" 
        showBackButton 
        onBackPress={onNavigateBack}
      />
      
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Choose Your Plan</Text>
          <Text style={styles.subtitle}>
            Unlock premium features like Person Count Management and Waiting List
          </Text>

          {/* Debug Test Button - Remove in production */}
          <TouchableOpacity
            style={{
              backgroundColor: '#007AFF',
              padding: 10,
              borderRadius: 5,
              marginTop: 10,
              alignItems: 'center'
            }}
            onPress={() => {
              console.log('🧪 Manual Razorpay test triggered');
              const testResult = razorpayService.testRazorpayModule();
              Alert.alert(
                'Razorpay Test Result',
                `Success: ${testResult.success}\nPlatform: ${testResult.details.platform}\nModule Available: ${testResult.details.moduleExists}\nHas Open Method: ${testResult.details.hasOpenMethod}`,
                [{ text: 'OK' }]
              );
            }}
          >
            <Text style={{ color: 'white', fontSize: 12 }}>🧪 Test Razorpay</Text>
          </TouchableOpacity>
        </View>

        {plans.length === 0 ? (
          <Card style={styles.noPlansCard}>
            <Text style={styles.noPlansText}>
              No subscription plans are currently available.
            </Text>
            <Text style={styles.noPlansSubtext}>
              Premium features are free for all users.
            </Text>
          </Card>
        ) : (
          <View style={styles.plansContainer}>
            {plans.map(renderPlan)}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    marginBottom: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  plansContainer: {
    gap: 16,
  },
  planCard: {
    padding: 20,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  planPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B00',
  },
  planDuration: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  planDescription: {
    fontSize: 16,
    color: '#333',
    marginBottom: 16,
    lineHeight: 22,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  featureItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  purchaseButton: {
    marginTop: 8,
  },
  statusCard: {
    padding: 20,
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#10B981',
    marginBottom: 16,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  statusButton: {
    marginTop: 16,
  },
  noPlansCard: {
    padding: 20,
    alignItems: 'center',
  },
  noPlansText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  noPlansSubtext: {
    fontSize: 16,
    color: '#10B981',
    textAlign: 'center',
  },
});
