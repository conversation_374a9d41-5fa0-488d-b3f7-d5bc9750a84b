export interface User {
  id: number;
  name: string;
  email: string;
  profile_picture?: string;
  created_at: string;
  has_pin?: boolean;
  is_admin?: boolean;
}

export interface RestaurantUser {
  id: number;
  username: string;
  mobile_number: string;
  total_users_count?: number;
  status: 'waiting' | 'dine-in';
  added_by: {
    id: number;
    name: string;
    email: string;
  };
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface RestaurantUserFormData {
  username: string;
  mobile_number: string;
  total_users_count?: number;
  status?: 'waiting' | 'dine-in';
}

export interface SubscriptionPlan {
  id: number;
  name: string;
  duration_days: number;
  price: number;
  is_enabled: boolean;
  description?: string;
  features?: string[];
  sort_order: number;
  formatted_price: string;
  formatted_duration: string;
  created_at: string;
  updated_at: string;
}

export interface UserSubscription {
  id: number;
  user_id: number;
  subscription_plan_id: number;
  starts_at: string;
  expires_at: string;
  status: 'active' | 'expired' | 'cancelled';
  amount_paid: number;
  payment_method?: string;
  transaction_id?: string;
  plan_snapshot?: any;
  days_remaining: number;
  subscription_plan?: SubscriptionPlan;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionStatus {
  has_premium_access: boolean;
  active_subscription: UserSubscription | null;
  subscription_required: boolean;
}

export interface PurchaseSubscriptionData {
  subscription_plan_id: number;
  payment_method?: string;
  transaction_id?: string;
  payment_details?: {
    razorpay_payment_id?: string;
    razorpay_order_id?: string;
    razorpay_signature?: string;
    amount_paid?: number;
    currency?: string;
  };
}

export type RootStackParamList = {
  Splash: undefined;
  Login: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Settings: undefined;
};

export type DashboardStackParamList = {
  UserList: undefined;
  AddUser: undefined;
  UserDetails: { userId: number };
};
